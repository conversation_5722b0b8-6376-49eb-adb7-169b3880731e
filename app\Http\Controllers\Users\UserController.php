<?php

namespace App\Http\Controllers\Users;

use App\Enums\User\UserStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Users\StoreUserRequest;
use App\Http\Requests\Users\UpdateUserRequest;
use App\Http\Requests\Users\UpdateUserStatusRequest;
use App\Http\Resources\Users\UserResource;
use App\Models\AdminProfile;
use App\Models\Company;
use App\Models\Headquarter;
use App\Models\Role;
use App\Models\Team;
use App\Models\User;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', User::class);

        $currentUserLevel = auth()->user()->getHighestRoleLevel();

        $query = User::query()
            ->excludeCurrent()
            ->select('id', 'uuid', 'username', 'email', 'status', 'updated_at', 'updated_by')
            ->with(['roles:id,name,level,is_headquarter', 'updatedBy:id,username'])
            ->whereHas('roles', function ($query) use ($currentUserLevel) {
                $query->maxLevel($currentUserLevel);
            });

        if (auth()->user()->getAccessibleHeadquarterIds()->isNotEmpty() || auth()->user()->getAccessibleCompanyIds()->isNotEmpty() || auth()->user()->getAccessibleTeamIds()->isNotEmpty()) {
            $query->whereHas('adminProfiles', function ($profileQuery) {
                $headquarterIds = auth()->user()->getAccessibleHeadquarterIds();
                $companyIds = auth()->user()->getAccessibleCompanyIds();
                $teamIds = auth()->user()->getAccessibleTeamIds();

                $profileQuery->whereHas('headquarters', function ($hq) use ($headquarterIds) {
                    $hq->whereIn('id', $headquarterIds);
                })
                    ->orWhereHas('companies', function ($company) use ($companyIds) {
                        $company->whereIn('id', $companyIds);
                    })
                    ->orWhereHas('teams', function ($team) use ($teamIds) {
                        $team->whereIn('id', $teamIds);
                    });
            });
        }

        if ($request->filled('headquarter_name') || $request->filled('company_name') || $request->filled('team_name')) {
            $query->whereHas('adminProfiles', function ($profileQuery) use ($request) {
                if ($request->filled('headquarter_name')) {
                    $searchTerm = '%'.$request->input('headquarter_name').'%';
                    $profileQuery->where(function ($q) use ($searchTerm) {
                        $q->whereHas('headquarters', function ($hq) use ($searchTerm) {
                            $hq->where('display_name', 'like', $searchTerm)->select('id', 'display_name');
                        })
                            ->orWhereHas('companies', function ($company) use ($searchTerm) {
                                $company->whereHas('parent', function ($hq) use ($searchTerm) {
                                    $hq->where('display_name', 'like', $searchTerm)->select('id', 'display_name');
                                })->select('id', 'headquarter_id');
                            })
                            ->orWhereHas('teams', function ($team) use ($searchTerm) {
                                $team->whereHas('companies', function ($company) use ($searchTerm) {
                                    $company->whereHas('parent', function ($hq) use ($searchTerm) {
                                        $hq->where('display_name', 'like', $searchTerm)->select('id', 'display_name');
                                    })->select('id', 'headquarter_id');
                                })->select('id');
                            });
                    });
                }

                if ($request->filled('company_name')) {
                    $searchTerm = '%'.$request->input('company_name').'%';
                    $profileQuery->where(function ($q) use ($searchTerm) {
                        $q->whereHas('companies', function ($company) use ($searchTerm) {
                            $company->where('display_name', 'like', $searchTerm)->where('is_headquarter', false)->select('id', 'display_name');
                        })
                            ->orWhereHas('teams', function ($team) use ($searchTerm) {
                                $team->whereHas('companies', function ($company) use ($searchTerm) {
                                    $company->where('display_name', 'like', $searchTerm)->where('is_headquarter', false)->select('id', 'display_name');
                                })->select('id');
                            });
                    });
                }

                if ($request->filled('team_name')) {
                    $searchTerm = '%'.$request->input('team_name').'%';
                    $profileQuery->whereHas('teams', function ($team) use ($searchTerm) {
                        $team->where('name', 'like', $searchTerm)->select('id', 'name');
                    });
                }
            });
        }

        $this->applySearchFilter($query, $request, 'username');
        $this->applyRelationFilter($query, $request, 'role', 'roles', 'name');
        $this->applyStatusFilter($query, $request);
        $this->applyUserSorting($query, $request);

        $users = $this->applyPagination($query, $request, 10,
            fn ($user) => (new UserResource($user))->toArray($request));

        return Inertia::render('users/Index', [
            'users' => $users,
            'filters' => $request->only(['username', 'headquarter_name', 'company_name', 'team_name', 'role', 'status', 'per_page', 'sort_field', 'sort_direction']),
            'availableRoles' => Role::getAvailableRoles($currentUserLevel),
            'statuses' => UserStatus::options(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->authorize('create', User::class);

        return Inertia::render('users/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(false),
            'teams' => Team::getForDropdown(),
            'roles' => auth()->user()->getForAssignableRoles(),
            'defaultStatus' => UserStatus::ACTIVE->value,
            'statuses' => UserStatus::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request): RedirectResponse
    {
        $this->authorize('create', User::class);

        $validated = $request->validated();

        try {
            DB::beginTransaction();

            $user = User::create([
                'username' => $validated['username'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'status' => $validated['status'],
            ]);

            $user->syncRoles([$validated['role']]);

            $adminProfile = AdminProfile::create([
                'uuid' => Str::uuid(),
            ]);

            $adminProfile->users()->attach($user->id);

            if ($validated['team_id']) {
                $adminProfile->teams()->attach($validated['team_id']);
            } elseif ($validated['company_id']) {
                $adminProfile->companies()->attach($validated['company_id']);
            } elseif ($validated['headquarter_id']) {
                $adminProfile->headquarters()->attach($validated['headquarter_id']);
            }

            DB::commit();

            return redirect()->route('users.index')->with('success', 'User created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->with('error', 'Failed to create user: '.$e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user): Response
    {
        $this->authorize('view', $user);

        $user->load(['roles', 'updatedBy', 'createdBy']);

        return Inertia::render('users/Show', [
            'user' => (new UserResource($user))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user): Response
    {
        $this->authorize('update', $user);

        $adminProfile = $user->adminProfiles->first();
        $headquarterId = $companyId = $teamId = null;

        if (! $adminProfile) {
            // No admin profile, all IDs remain null
        } elseif ($headquarter = $adminProfile->headquarters->first()) {
            $headquarterId = $headquarter->id;
        } elseif ($company = $adminProfile->companies->first()) {
            $companyId = $company->id;
            $headquarterId = $company->headquarter_id && $company->parent ? $company->parent->id : null;
        } elseif ($team = $adminProfile->teams->first()) {
            $teamId = $team->id;

            if ($teamCompany = $team->companies->first()) {
                $companyId = $teamCompany->id;
                $headquarterId = $teamCompany->headquarter_id && $teamCompany->parent ? $teamCompany->parent->id : null;
            }
        }

        return Inertia::render('users/Edit', [
            'user' => [
                'id' => $user->id,
                'uuid' => $user->uuid,
                'username' => $user->username,
                'email' => $user->email,
                'status' => $user->status->value,
                'roles' => $user->roles->pluck('id'),
            ],
            'adminProfile' => $adminProfile ? [
                'id' => $adminProfile->id,
                'code' => $adminProfile->code,
                'name' => $adminProfile->name,
                'headquarter_id' => $headquarterId,
                'company_id' => $companyId,
                'team_id' => $teamId,
            ] : null,
            'roles' => auth()->user()->getForAssignableRoles(),
            'statuses' => UserStatus::options(),
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(false),
            'teams' => Team::getForDropdown(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user): RedirectResponse
    {
        $this->authorize('update', $user);

        $validated = $request->validated();

        $adminProfile = $user->adminProfiles()->exists();

        try {
            DB::beginTransaction();

            $user->update([
                'username' => $validated['username'],
                'email' => $validated['email'],
                'status' => $validated['status'],
            ]);

            if ($request->filled('password')) {
                $user->update(['password' => Hash::make($validated['password'])]);
            }

            $user->syncRoles([$validated['role']]);

            if (! $adminProfile) {
                $adminProfile = AdminProfile::create([
                    'uuid' => Str::uuid(),
                ]);

                $adminProfile->users()->attach($user->id);
            }

            DB::commit();

            return redirect()->route('users.index')->with('success', 'User updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->with('error', 'Failed to update user: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        $this->authorize('delete', $user);

        try {
            DB::beginTransaction();

            $adminProfile = $user->adminProfiles()->exists();

            if ($adminProfile) {
                $user->adminProfiles()->detach();
                $user->adminProfiles()->delete();
            }

            $user->delete();

            DB::commit();

            return redirect()->route('users.index')->with('success', 'User deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()->with('error', 'Failed to delete user. '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateUserStatusRequest $request, User $user): RedirectResponse
    {
        $this->authorize('update', $user);

        try {
            $user->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'User status updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to update user status. '.$e->getMessage());
        }
    }

    /**
     * Apply custom sorting for users with morph relationships
     */
    protected function applyUserSorting($query, Request $request, string $defaultField = 'created_at', string $defaultDirection = 'desc')
    {
        $sortField = $request->input('sort_field', $defaultField);
        $sortDirection = $request->input('sort_direction', $defaultDirection);

        switch ($sortField) {
            case 'headquarter:display_name':
                $this->sortByHeadquarterName($query, $sortDirection);
                break;
            case 'company:display_name':
                $this->sortByCompanyName($query, $sortDirection);
                break;
            case 'team:name':
                $this->sortByTeamName($query, $sortDirection);
                break;
            default:
                $this->applySorting($query, $request, $defaultField, $defaultDirection);
                break;
        }

        return $query;
    }

    /**
     * Sort users by headquarter name
     */
    protected function sortByHeadquarterName($query, string $direction = 'asc')
    {
        return $query->orderBy(
            DB::raw("(
                SELECT COALESCE(
                    hq_direct.display_name,
                    hq_from_company.display_name,
                    hq_from_team.display_name
                )
                FROM userables u
                JOIN admin_profiles ap ON u.userable_id = ap.id
                LEFT JOIN headquarterables hq_rel ON ap.id = hq_rel.headquarterable_id
                    AND hq_rel.headquarterable_type = '" . AdminProfile::class . "'
                LEFT JOIN headquarters hq_direct ON hq_rel.headquarter_id = hq_direct.id
                LEFT JOIN companyables comp_rel ON ap.id = comp_rel.companyable_id
                    AND comp_rel.companyable_type = '" . AdminProfile::class . "'
                LEFT JOIN companies comp ON comp_rel.company_id = comp.id
                LEFT JOIN headquarters hq_from_company ON comp.headquarter_id = hq_from_company.id
                LEFT JOIN teamables team_rel ON ap.id = team_rel.teamable_id
                    AND team_rel.teamable_type = '" . AdminProfile::class . "'
                LEFT JOIN teams t ON team_rel.team_id = t.id
                LEFT JOIN companyables team_comp_rel ON t.id = team_comp_rel.companyable_id
                    AND team_comp_rel.companyable_type = '" . Team::class . "'
                LEFT JOIN companies team_comp ON team_comp_rel.company_id = team_comp.id
                LEFT JOIN headquarters hq_from_team ON team_comp.headquarter_id = hq_from_team.id
                WHERE u.user_id = users.id
                    AND u.userable_type = '" . AdminProfile::class . "'
                LIMIT 1
            )"),
            $direction
        );
    }

    /**
     * Sort users by company name
     */
    protected function sortByCompanyName($query, string $direction = 'asc')
    {
        return $query->orderBy(
            DB::raw("(
                SELECT COALESCE(
                    comp_direct.display_name,
                    team_comp.display_name
                )
                FROM userables u
                JOIN admin_profiles ap ON u.userable_id = ap.id
                LEFT JOIN companyables comp_rel ON ap.id = comp_rel.companyable_id
                    AND comp_rel.companyable_type = '" . AdminProfile::class . "'
                LEFT JOIN companies comp_direct ON comp_rel.company_id = comp_direct.id
                    AND comp_direct.is_headquarter = 0
                LEFT JOIN teamables team_rel ON ap.id = team_rel.teamable_id
                    AND team_rel.teamable_type = '" . AdminProfile::class . "'
                LEFT JOIN teams t ON team_rel.team_id = t.id
                LEFT JOIN companyables team_comp_rel ON t.id = team_comp_rel.companyable_id
                    AND team_comp_rel.companyable_type = '" . Team::class . "'
                LEFT JOIN companies team_comp ON team_comp_rel.company_id = team_comp.id
                    AND team_comp.is_headquarter = 0
                WHERE u.user_id = users.id
                    AND u.userable_type = '" . AdminProfile::class . "'
                LIMIT 1
            )"),
            $direction
        );
    }

    /**
     * Sort users by team name
     */
    protected function sortByTeamName($query, string $direction = 'asc')
    {
        return $query->orderBy(
            DB::raw("(
                SELECT t.name
                FROM userables u
                JOIN admin_profiles ap ON u.userable_id = ap.id
                LEFT JOIN teamables team_rel ON ap.id = team_rel.teamable_id
                    AND team_rel.teamable_type = '" . AdminProfile::class . "'
                LEFT JOIN teams t ON team_rel.team_id = t.id
                WHERE u.user_id = users.id
                    AND u.userable_type = '" . AdminProfile::class . "'
                LIMIT 1
            )"),
            $direction
        );
    }
}
