<?php

namespace App\Models;

use App\Enums\AccessControl\RoleName;
use App\Enums\User\UserLocale;
use App\Enums\User\UserStatus;
use App\Traits\HasAuditFields;
use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements Auditable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasAuditFields, HasFactory, HasRoles, HasUuid, Notifiable, SoftDeletes;

    use \OwenIt\Auditing\Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'uuid',
        'username',
        'email',
        'email_verified_at',
        'password',
        'status',
        'last_login_time',
        'last_login_ip',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'status' => UserStatus::class,
            'locale' => UserLocale::class,
            'last_login_time' => 'datetime',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Scope a query to exclude the currently authenticated user.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExcludeCurrent(Builder $query)
    {
        return $query->where('id', '!=', Auth::id());
    }

    /**
     * Get all admin profiles associated with this user.
     */
    public function adminProfiles(): MorphToMany
    {
        return $this->morphedByMany(AdminProfile::class, 'userable');
    }

    /**
     * Update user's last login information
     */
    public function updateLoginInfo(string $ip): void
    {
        $this->updateQuietly([
            'last_login_time' => now(),
            'last_login_ip' => $ip,
        ]);
    }

    /**
     * Get the user's highest role level
     */
    public function getHighestRoleLevel(): int
    {
        return $this->roles()->max('level') ?? 0;
    }

    /**
     * Check if the user can see roles at the same level based on their highest role
     */
    public function canSeeSameLevel(): bool
    {
        return $this->roles()->first()->can_see_same_level ?? false;
    }

    /**
     * Get roles that this user can assign to others
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAssignableRoles()
    {
        $userLevel = $this->getHighestRoleLevel();
        $canSeeSameLevel = $this->canSeeSameLevel();

        $query = Role::query();

        if ($canSeeSameLevel) {
            $query->where('level', '<=', $userLevel);
        } else {
            $query->where('level', '<', $userLevel);
        }

        return $query->get();
    }

    /**
     * Get formatted assignable roles for dropdown
     */
    public function getForAssignableRoles(): array
    {
        return $this->getAssignableRoles()->map(fn ($role) => [
            'id' => $role->id,
            'name' => $role->name,
            'is_required_headquarter' => $role->is_required_headquarter,
            'is_required_company' => $role->is_required_company,
            'is_required_team' => $role->is_required_team,
            'is_headquarter' => $role->is_headquarter,
        ])->toArray();
    }

    /**
     * Check if the user is a headquarter.
     */
    public function isHeadquarter(): bool
    {
        return $this->roles->pluck('is_headquarter')->unique()->contains(true);
    }

    /**
     * Check if the user is a super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole(RoleName::SUPER_ADMIN);
    }

    /**
     * Check if the user has access to the given company ID.
     */
    public function hasCompanyAccess(?int $companyId): bool
    {
        $resolved = $this->getResolveAccessHierarchy();

        return $this->isHeadquarter() && $resolved['company'] === $companyId;
    }

    /**
     * Check if the user has access to the given team ID.
     */
    public function hasTeamAccess(?int $teamId): bool
    {
        $resolved = $this->getResolveAccessHierarchy();

        return $this->isHeadquarter() && $resolved['team'] === $teamId;
    }

    /**
     * Get hierarchy with full objects for internal use.
     */
    public function getResolveHierarchy(): array
    {
        return $this->resolveHierarchy(false, false);
    }

    /**
     * Get hierarchy with IDs for access control checks.
     */
    public function getResolveAccessHierarchy(): array
    {
        return $this->resolveHierarchy(true, true);
    }

    /**
     * Generic hierarchy resolver with flexible return and logic options.
     */
    protected function resolveHierarchy(bool $returnIds = false, bool $forAccess = false): array
    {
        $profile = $this->adminProfiles()->first();

        if (! $profile) {
            return $this->emptyHierarchy();
        }

        if ($hq = $profile->headquarters->first()) {
            return [
                'headquarter' => $returnIds ? $hq->id : $hq,
                'company' => null,
                'team' => null,
            ];
        }

        if ($company = $profile->companies->first()) {
            $headquarter = ($company->headquarter_id && $company->parent)
                ? ($returnIds ? $company->parent->id : $company->parent)
                : null;

            return [
                'headquarter' => $headquarter,
                'company' => $returnIds ? $company->id : $company,
                'team' => null,
            ];
        }

        if ($team = $profile->teams->first()) {
            $company = $team->companies->first();

            if (! $company) {
                return $this->emptyHierarchy();
            }

            $headquarter = ($company->headquarter_id && $company->parent)
                ? ($returnIds ? $company->parent->id : $company->parent)
                : null;

            if (! $forAccess && $company->is_headquarter) {
                return [
                    'headquarter' => $headquarter,
                    'company' => null,
                    'team' => $team,
                ];
            }

            return [
                'headquarter' => $headquarter,
                'company' => $returnIds ? $company->id : $company,
                'team' => $returnIds ? $team->id : $team,
            ];
        }

        return $this->emptyHierarchy();
    }

    /**
     * Default structure for unresolved hierarchy.
     */
    protected function emptyHierarchy(): array
    {
        return ['headquarter' => null, 'company' => null, 'team' => null];
    }

    /**
     * The "booting" method of the model.
     * This method is now handled by the HasUuid, HasAuditFields traits
     */
    protected static function boot()
    {
        parent::boot();
    }

    /**
     * Get the accessible headquarter IDs for the user.
     */
    public function getAccessibleHeadquarterIds(): Collection
    {
        return $this->adminProfiles->flatMap(function ($profile) {
            return $profile->getAccessibleHeadquarterIds($this);
        })->unique();
    }

    /**
     * Get the accessible company IDs for the user.
     */
    public function getAccessibleCompanyIds(): Collection
    {
        return $this->adminProfiles->flatMap(function ($profile) {
            return $profile->getAccessibleCompanyIds($this);
        })->unique();
    }

    /**
     * Get the accessible team IDs for the user.
     */
    public function getAccessibleTeamIds(): Collection
    {
        return $this->adminProfiles->flatMap(function ($profile) {
            return $profile->getAccessibleTeamIds($this);
        })->unique();
    }
}
