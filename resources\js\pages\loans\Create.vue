<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import FormField from '@/components/form/FormField.vue';
import FormInputSearch from '@/components/form/FormInputSearch.vue';
import Heading from '@/components/Heading.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import FormSelect from '@/components/form/FormSelect.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import FaIcon from '@/components/FaIcon.vue';
import { computed, ref } from 'vue';

const { formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

interface Company {
    id: number;
    display_name: string;
    code: string;
    parent_id: number | null;
}

interface Team {
    id: number;
    name: string;
    company_id: number | null;
    company_name: string | null;
}

interface Agent {
    id: number;
    name: string;
    code: string;
}

interface Props {
    companies: Company[];
    teams: Team[];
    headquarters: Company[];
    agents: Agent[];
    loanType: Array<{
        id: number;
        value: string;
    }>;
}

const props = defineProps<Props>();

const form = useForm({
    headquarter_id: null as number | null,
    company_id: null as number | null,
    team_id: null as number | null,
    customer_id: null as string | null,
    agent_id: null as number | null,
    selection_type_id: null as number | null,
    search_input: null as string | null,
    search_value: null as string | null,
});

const handleResults = (newSearch: string) => {
    console.log('Results from child:', newSearch);
    form.search_input = newSearch; // if you want to store in form
};

const formFields = computed(() => [
    {
        id: 'headquarter_id',
        label: 'Headquarter Name',
        type: 'select',
        required: true,
        placeholder: 'Headquarter Name',
        error: form.errors.headquarter_id,
        options: formatSelectionOptions(props.headquarters, 'id', 'display_name'),
        modelValue: form.headquarter_id,
        updateValue: (value: number) => (form.headquarter_id = value),
    },
    {
        id: 'company_id',
        label: 'Company Name',
        type: 'select',
        required: true,
        placeholder: 'Company Name',
        error: form.errors.company_id,
        options: formatSelectionOptions(filteredCompanies.value, 'id', 'display_name'),
        modelValue: form.company_id,
        updateValue: (value: number) => (form.company_id = value),
    },
    {
        id: 'team_id',
        label: 'Team Name',
        type: 'select',
        required: true,
        placeholder: 'Team Name',
        error: form.errors.team_id,
        options: formatSelectionOptions(filteredTeams.value, 'id', 'name'),
        modelValue: form.team_id,
        updateValue: (value: number) => (form.team_id = value),
    },
    {
        id: 'agent_id',
        label: 'Agent',
        type: 'select',
        required: true,
        placeholder: 'Agent',
        error: form.errors.agent_id,
        options: formatSelectionOptions(filteredAgents.value, 'id', 'name'),
        modelValue: form.agent_id,
        updateValue: (value: number) => (form.agent_id = value),
    },
]);

const formFields2 = computed(() => [
    {
        id: 'customer_id',
        label: 'Customer Name',
        type: 'input_search',
        required: true,
        placeholder: 'Customer Name',
        error: form.errors.customer_id,
        modelValue: form.customer_id,
        updateValue: (value: string) => (form.customer_id = value),
    },
    {
        id: 'selection_type_id',
        label: 'Loan Type',
        type: 'select',
        required: true,
        placeholder: 'Loan Type',
        error: form.errors.selection_type_id,
        options: formatSelectionOptions(props.loanType),
        modelValue: form.selection_type_id,
        updateValue: (value: number) => (form.selection_type_id = value),
    },
]);
// Filter companies based on selected headquarter
const filteredCompanies = computed(() => {
    if (!form.headquarter_id) return [];
    return props.companies.filter((company) => company.parent_id === form.headquarter_id);
});

// Filter teams based on selected company
const filteredTeams = computed(() => {
    if (!form.company_id) return [];
    return props.teams.filter((team) => team?.company_id === form.company_id);
});

const filteredAgents = computed(() => {
    if (!form.team_id) return [];
    return props.agents.filter((agent) => agent.teams?.some((team) => team.id === form.team_id));
});

const tabItems = computed(() => [
    { label: 'Collateral Info', value: 'info' },
    { label: 'Valuation', value: 'valuation' },
    { label: 'Additional Owner', value: 'owner' },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('loans.store'),
            entityName: 'loan',
        },
    });
};

</script>

<template>
    <AppLayout>
        <Head title="Create Loan" />
        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000011" description="Create a new loan record" />
            <form @submit.prevent="submit">
                <Card class="gap-0 py-0">
                    <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                        <CardTitle>Add New Loan</CardTitle>
                    </CardHeader>
                <TabsWrapper v-model="activeTab" :tabs="tabItems">
                <template #info>
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                            />
                            <div class="gap-2">
                                <Label>
                                    Customer Name
                                <RequiredIndicator v-if="true" />
                                </Label>
                                <!-- <FormInputSearch api="/api/loan-api" @resultsUpdated="handleResults" iconPosition="right"></FormInputSearch> -->
                            </div>
                            <div class="gap-2">
                                <Label>
                                   Loan Type
                                <RequiredIndicator v-if="true" />
                                </Label>
                                <FormSelect
                                    :id="props.id"
                                    :label="props.label"
                                    :model-value="form.selection_type_id"
                                    @update:model-value="(value: number) => (form.selection_type_id = value)"
                                    :options="formatSelectionOptions(props.loanType)"
                                    placeholder="Loan Type"
                                    :error="form.errors.selection_type_id"
                                />
                            </div>
                        </div>
                    </div>
                </section>
                <CardContent class="py-4 px-0">
                    <Accordion type="single" class="w-full" collapsible>
                        <AccordionItem value="item-1" class="mb-1">
                            <Card class="gap-0 rounded-xs py-0">
                                <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                    <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                        <FaIcon name="plus" />
                                    </span>
                                    <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                        <FaIcon name="minus" />
                                    </span>
                                    <span class="flex-1 py-2 text-left font-medium"> Valuation </span>
                                </AccordionTrigger>

                                <Separator />
                                <AccordionContent class="p-2">
                                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div>
                                            <Label class="text-base">Valuer <RequiredIndicator /></Label>
                                        </div>
                                    </div>
                                </AccordionContent>
                            </Card>
                        </AccordionItem>
                    </Accordion>
                </CardContent>
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <div class="gap-2">
                                <Label>
                                    Customer Name
                                <RequiredIndicator v-if="true" />
                                </Label>
                                <!-- <FormInputSearch api="/api/loan-api" @resultsUpdated="handleResults" iconPosition="right"></FormInputSearch> -->
                            </div>
                        </div>
                    </div>
                </section>
                <CardContent class="py-4 px-0">
                    <Accordion type="single" class="w-full" collapsible>
                        <AccordionItem value="item-1" class="mb-1">
                            <Card class="gap-0 rounded-xs py-0">
                                <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                    <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                        <FaIcon name="plus" />
                                    </span>
                                    <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                        <FaIcon name="minus" />
                                    </span>
                                    <span class="flex-1 py-2 text-left font-medium"> Valuation </span>
                                </AccordionTrigger>

                                <Separator />
                                <AccordionContent class="p-2">
                                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div>
                                            <Label class="text-base">Valuer <RequiredIndicator /></Label>
                                        </div>
                                    </div>
                                </AccordionContent>
                            </Card>
                        </AccordionItem>
                    </Accordion>
                </CardContent>
                </template>
                </TabsWrapper>
                
            </Card>
            </form>
        </div>
    </AppLayout>
</template>
